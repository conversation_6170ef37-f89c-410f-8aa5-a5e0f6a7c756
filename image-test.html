<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Loading Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .image-test { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .image-test img { max-width: 200px; height: auto; margin: 10px; border: 2px solid #ccc; }
        .image-test.error { border-color: #dc3545; background-color: #f8d7da; }
        .image-test.success { border-color: #28a745; background-color: #d4edda; }
        .status { font-weight: bold; margin: 10px 0; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        .loading { color: #ffc107; }
    </style>
</head>
<body>
    <h1>Image Loading Test</h1>
    <p>This page tests all the images referenced in the main website to identify loading issues.</p>
    
    <div id="test-results"></div>
    
    <script>
        const imageTests = [
            { name: "Profile Image", src: "./fuji/mdyao_fuji.png", alt: "Mingde Yao Profile" },
            { name: "PolarFree (2025)", src: "./papers/2025/PolarFree.png", alt: "PolarFree" },
            { name: "Uni-ISP (2025)", src: "./papers/2025/Uni-ISP.png", alt: "Uni-ISP" },
            { name: "NeSR (2024)", src: "./papers/2024/NeSR.png", alt: "Neural Spectral Reconstruction" },
            { name: "DR-Restore (2023)", src: "./papers/2023/DR-Restore.png", alt: "Neural Degradation Restoration" },
            { name: "CSN (2023)", src: "./papers/2023/CSN.png", alt: "Channel Selective Normalization" },
            { name: "ZeDuSR (2023)", src: "./papers/2023/ZeDuSR.png", alt: "Zero-Shot Dual-Lens Super-Resolution" },
            { name: "OE19 (2019)", src: "./papers/2019/OE19.png", alt: "Spectral-depth imaging" }
        ];
        
        function createImageTest(testInfo) {
            const container = document.createElement('div');
            container.className = 'image-test';
            container.innerHTML = `
                <h3>${testInfo.name}</h3>
                <div class="status loading">⏳ Loading...</div>
                <p><strong>Path:</strong> <code>${testInfo.src}</code></p>
                <img src="${testInfo.src}" alt="${testInfo.alt}" />
            `;
            
            const img = container.querySelector('img');
            const status = container.querySelector('.status');
            
            img.addEventListener('load', function() {
                status.innerHTML = '✅ <span class="success">Successfully loaded</span>';
                status.className = 'status success';
                container.classList.add('success');
                console.log(`✅ Loaded: ${testInfo.src}`);
            });
            
            img.addEventListener('error', function() {
                status.innerHTML = '❌ <span class="error">Failed to load</span>';
                status.className = 'status error';
                container.classList.add('error');
                console.error(`❌ Failed: ${testInfo.src}`);
                
                // Try to provide more information about the error
                fetch(testInfo.src, { method: 'HEAD' })
                    .then(response => {
                        if (!response.ok) {
                            status.innerHTML += `<br><small>HTTP ${response.status}: ${response.statusText}</small>`;
                        }
                    })
                    .catch(error => {
                        status.innerHTML += `<br><small>Network error: ${error.message}</small>`;
                    });
            });
            
            return container;
        }
        
        function runTests() {
            const resultsContainer = document.getElementById('test-results');
            
            imageTests.forEach(testInfo => {
                const testElement = createImageTest(testInfo);
                resultsContainer.appendChild(testElement);
            });
            
            // Summary after all tests
            setTimeout(() => {
                const successCount = document.querySelectorAll('.image-test.success').length;
                const errorCount = document.querySelectorAll('.image-test.error').length;
                const totalCount = imageTests.length;
                
                const summary = document.createElement('div');
                summary.style.cssText = 'margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center;';
                summary.innerHTML = `
                    <h2>Test Summary</h2>
                    <p><strong>Total Images:</strong> ${totalCount}</p>
                    <p><strong>Successfully Loaded:</strong> <span class="success">${successCount}</span></p>
                    <p><strong>Failed to Load:</strong> <span class="error">${errorCount}</span></p>
                    ${errorCount > 0 ? '<p style="color: #dc3545;">⚠️ Some images failed to load. Check the console for details.</p>' : '<p style="color: #28a745;">🎉 All images loaded successfully!</p>'}
                `;
                
                resultsContainer.appendChild(summary);
                
                console.log(`\n📊 Test Summary: ${successCount}/${totalCount} images loaded successfully`);
            }, 3000);
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
